<?php
/**
 * Odessa Dentists Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function odd_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'odd'),
    ));
}
add_action('after_setup_theme', 'odd_theme_setup');

/**
 * Enqueue scripts and styles
 */
function odd_scripts() {
    wp_enqueue_style('odd-style', get_stylesheet_uri());
    wp_enqueue_script('odd-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
}
add_action('wp_enqueue_scripts', 'odd_scripts');

/**
 * Register Custom Post Type: Listing
 */
function register_listing_post_type() {
    $labels = array(
        'name'                  => _x('Listings', 'Post Type General Name', 'odd'),
        'singular_name'         => _x('Listing', 'Post Type Singular Name', 'odd'),
        'menu_name'             => __('Listings', 'odd'),
        'name_admin_bar'        => __('Listing', 'odd'),
        'archives'              => __('Listing Archives', 'odd'),
        'attributes'            => __('Listing Attributes', 'odd'),
        'parent_item_colon'     => __('Parent Listing:', 'odd'),
        'all_items'             => __('All Listings', 'odd'),
        'add_new_item'          => __('Add New Listing', 'odd'),
        'add_new'               => __('Add New', 'odd'),
        'new_item'              => __('New Listing', 'odd'),
        'edit_item'             => __('Edit Listing', 'odd'),
        'update_item'           => __('Update Listing', 'odd'),
        'view_item'             => __('View Listing', 'odd'),
        'view_items'            => __('View Listings', 'odd'),
        'search_items'          => __('Search Listing', 'odd'),
        'not_found'             => __('Not found', 'odd'),
        'not_found_in_trash'    => __('Not found in Trash', 'odd'),
        'featured_image'        => __('Featured Image', 'odd'),
        'set_featured_image'    => __('Set featured image', 'odd'),
        'remove_featured_image' => __('Remove featured image', 'odd'),
        'use_featured_image'    => __('Use as featured image', 'odd'),
        'insert_into_item'      => __('Insert into listing', 'odd'),
        'uploaded_to_this_item' => __('Uploaded to this listing', 'odd'),
        'items_list'            => __('Listings list', 'odd'),
        'items_list_navigation' => __('Listings list navigation', 'odd'),
        'filter_items_list'     => __('Filter listings list', 'odd'),
    );
    
    $args = array(
        'label'                 => __('Listing', 'odd'),
        'description'           => __('Dental practice listings', 'odd'),
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'taxonomies'            => array('directory'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'query_var'             => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-location-alt',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => 'listings',
        //'rewrite'               => array( 'slug' => 'listings' ),
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
    );
    
    register_post_type('listing', $args);
}
add_action('init', 'register_listing_post_type', 0);

/**
 * Register Custom Taxonomy: Directory
 */
function register_directory_taxonomy() {
    $labels = array(
        'name'                       => _x('Directories', 'Taxonomy General Name', 'odd'),
        'singular_name'              => _x('Directory', 'Taxonomy Singular Name', 'odd'),
        'menu_name'                  => __('Directories', 'odd'),
        'all_items'                  => __('All Directories', 'odd'),
        'parent_item'                => __('Parent Directory', 'odd'),
        'parent_item_colon'          => __('Parent Directory:', 'odd'),
        'new_item_name'              => __('New Directory Name', 'odd'),
        'add_new_item'               => __('Add New Directory', 'odd'),
        'edit_item'                  => __('Edit Directory', 'odd'),
        'update_item'                => __('Update Directory', 'odd'),
        'view_item'                  => __('View Directory', 'odd'),
        'separate_items_with_commas' => __('Separate directories with commas', 'odd'),
        'add_or_remove_items'        => __('Add or remove directories', 'odd'),
        'choose_from_most_used'      => __('Choose from the most used', 'odd'),
        'popular_items'              => __('Popular Directories', 'odd'),
        'search_items'               => __('Search Directories', 'odd'),
        'not_found'                  => __('Not Found', 'odd'),
        'no_terms'                   => __('No directories', 'odd'),
        'items_list'                 => __('Directories list', 'odd'),
        'items_list_navigation'      => __('Directories list navigation', 'odd'),
    );
    
    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'query_var'                  => true,
        'rewrite'                    => array( 'slug' => 'directory' ),
    );
    
    register_taxonomy('directory', array('listing'), $args);
}
add_action('init', 'register_directory_taxonomy', 0);

/**
 * Register Custom Fields for Listings
 */
function register_listing_custom_fields() {
    // Basic Info Fields (removed: query, name, name_for_emails)
    $basic_fields = array(
        'listing_website', 'listing_subtypes', 'listing_category', 'listing_type'
    );

    // Contact Fields
    $contact_fields = array(
        'listing_phone', 'listing_email_1', 'listing_email_1_full_name',
        'listing_email_1_first_name', 'listing_email_1_last_name',
        'listing_email_1_title', 'listing_email_1_phone'
    );

    // Location Fields (removed: us_state, country_code, time_zone, plus_code; corrected: zip, lat, long)
    $location_fields = array(
        'listing_full_address', 'listing_borough', 'listing_street', 'listing_city',
        'listing_zip', 'listing_state', 'listing_country',
        'listing_lat', 'listing_long', 'listing_h3', 'listing_area_service'
    );

    // Business Data Fields (removed: photos_count, range)
    $business_fields = array(
        'listing_rating', 'listing_reviews', 'listing_reviews_link',
        'listing_photo', 'listing_street_view', 'listing_about',
        'listing_logo', 'listing_verified'
    );

    // Links Fields
    $link_fields = array(
        'listing_booking_appointment_link', 'listing_location_link', 'listing_location_reviews_link'
    );

    // ID Fields
    $id_fields = array(
        'listing_place_id', 'listing_google_id', 'listing_cid', 'listing_kgmid', 'listing_reviews_id'
    );

    // Social Media Fields
    $social_fields = array(
        'listing_facebook', 'listing_instagram', 'listing_linkedin',
        'listing_tiktok', 'listing_twitter', 'listing_youtube'
    );

    // Hours Fields
    $hours_fields = array(
        'listing_working_hours', 'listing_popular_times'
    );

    // Combine all fields
    $all_fields = array_merge(
        $basic_fields, $contact_fields, $location_fields,
        $business_fields, $link_fields, $id_fields,
        $social_fields, $hours_fields
    );

    // Register each field as a meta field
    foreach ($all_fields as $field) {
        register_meta('post', $field, array(
            'type' => 'string',
            'description' => 'Listing custom field: ' . $field,
            'single' => true,
            'show_in_rest' => true,
        ));
    }
}
add_action('init', 'register_listing_custom_fields');

/**
 * Register Custom Meta Fields for Directory Taxonomy
 */
function register_directory_meta_fields() {
    // Directory archive meta fields
    $directory_meta_fields = array(
        'directory_archive_heading',
        'directory_archive_description',
        'directory_archive_subheading',
        'directory_archive_content'
    );

    // Register each field as a term meta field
    foreach ($directory_meta_fields as $field) {
        register_term_meta('directory', $field, array(
            'type' => 'string',
            'description' => 'Directory archive field: ' . $field,
            'single' => true,
            'show_in_rest' => true,
        ));
    }
}
add_action('init', 'register_directory_meta_fields');

/**
 * Add Custom Fields Meta Box
 */
function add_listing_meta_boxes() {
    add_meta_box(
        'listing_basic_info',
        'Basic Information',
        'listing_basic_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_contact_info',
        'Contact Information',
        'listing_contact_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_location_info',
        'Location Information',
        'listing_location_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_business_info',
        'Business Information',
        'listing_business_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_social_info',
        'Social Media & Links',
        'listing_social_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_hours_info',
        'Working Hours',
        'listing_hours_info_callback',
        'listing',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_listing_meta_boxes');

/**
 * Add Custom Fields to Directory Taxonomy
 */
function add_directory_custom_fields() {
    // Hook into directory taxonomy add and edit forms
    add_action('directory_add_form_fields', 'directory_add_custom_fields');
    add_action('directory_edit_form_fields', 'directory_edit_custom_fields');
    add_action('edited_directory', 'save_directory_custom_fields');
    add_action('create_directory', 'save_directory_custom_fields');
}
add_action('init', 'add_directory_custom_fields');

/**
 * Add custom fields to directory taxonomy add form
 */
function directory_add_custom_fields() {
    ?>
    <div class="form-field">
        <label for="directory_archive_heading"><?php _e('Archive Heading', 'odd'); ?></label>
        <input type="text" name="directory_archive_heading" id="directory_archive_heading" value="" />
        <p class="description"><?php _e('Custom heading for the directory archive page', 'odd'); ?></p>
    </div>

    <div class="form-field">
        <label for="directory_archive_description"><?php _e('Archive Description', 'odd'); ?></label>
        <textarea name="directory_archive_description" id="directory_archive_description" rows="5" cols="50"></textarea>
        <p class="description"><?php _e('Custom description for the directory archive page', 'odd'); ?></p>
    </div>

    <div class="form-field">
        <label for="directory_archive_subheading"><?php _e('Archive Subheading', 'odd'); ?></label>
        <input type="text" name="directory_archive_subheading" id="directory_archive_subheading" value="" />
        <p class="description"><?php _e('Custom subheading for the directory archive page', 'odd'); ?></p>
    </div>

    <div class="form-field">
        <label for="directory_archive_content"><?php _e('Archive Content', 'odd'); ?></label>
        <textarea name="directory_archive_content" id="directory_archive_content" rows="8" cols="50"></textarea>
        <p class="description"><?php _e('Additional content for the directory archive page', 'odd'); ?></p>
    </div>
    <?php
}

/**
 * Add custom fields to directory taxonomy edit form
 */
function directory_edit_custom_fields($term) {
    // Get current values
    $archive_heading = get_term_meta($term->term_id, 'directory_archive_heading', true);
    $archive_description = get_term_meta($term->term_id, 'directory_archive_description', true);
    $archive_subheading = get_term_meta($term->term_id, 'directory_archive_subheading', true);
    $archive_content = get_term_meta($term->term_id, 'directory_archive_content', true);
    ?>
    <tr class="form-field">
        <th scope="row" valign="top">
            <label for="directory_archive_heading"><?php _e('Archive Heading', 'odd'); ?></label>
        </th>
        <td>
            <input type="text" name="directory_archive_heading" id="directory_archive_heading" value="<?php echo esc_attr($archive_heading); ?>" />
            <p class="description"><?php _e('Custom heading for the directory archive page', 'odd'); ?></p>
        </td>
    </tr>

    <tr class="form-field">
        <th scope="row" valign="top">
            <label for="directory_archive_description"><?php _e('Archive Description', 'odd'); ?></label>
        </th>
        <td>
            <textarea name="directory_archive_description" id="directory_archive_description" rows="5" cols="50"><?php echo esc_textarea($archive_description); ?></textarea>
            <p class="description"><?php _e('Custom description for the directory archive page', 'odd'); ?></p>
        </td>
    </tr>

    <tr class="form-field">
        <th scope="row" valign="top">
            <label for="directory_archive_subheading"><?php _e('Archive Subheading', 'odd'); ?></label>
        </th>
        <td>
            <input type="text" name="directory_archive_subheading" id="directory_archive_subheading" value="<?php echo esc_attr($archive_subheading); ?>" />
            <p class="description"><?php _e('Custom subheading for the directory archive page', 'odd'); ?></p>
        </td>
    </tr>

    <tr class="form-field">
        <th scope="row" valign="top">
            <label for="directory_archive_content"><?php _e('Archive Content', 'odd'); ?></label>
        </th>
        <td>
            <textarea name="directory_archive_content" id="directory_archive_content" rows="8" cols="50"><?php echo esc_textarea($archive_content); ?></textarea>
            <p class="description"><?php _e('Additional content for the directory archive page', 'odd'); ?></p>
        </td>
    </tr>
    <?php
}

/**
 * Save directory custom fields
 */
function save_directory_custom_fields($term_id) {
    // Security check
    if (!current_user_can('manage_categories')) {
        return;
    }

    // List of directory meta fields
    $meta_fields = array(
        'directory_archive_heading',
        'directory_archive_description',
        'directory_archive_subheading',
        'directory_archive_content'
    );

    // Save each field
    foreach ($meta_fields as $field) {
        if (isset($_POST[$field])) {
            $value = sanitize_text_field($_POST[$field]);
            // For textarea fields, use sanitize_textarea_field
            if (in_array($field, array('directory_archive_description', 'directory_archive_content'))) {
                $value = sanitize_textarea_field($_POST[$field]);
            }
            update_term_meta($term_id, $field, $value);
        }
    }
}

/**
 * Basic Info Meta Box Callback
 */
function listing_basic_info_callback($post) {
    wp_nonce_field('listing_meta_box', 'listing_meta_box_nonce');

    $fields = array(
        'listing_website' => 'Website',
        'listing_subtypes' => 'Subtypes',
        'listing_category' => 'Category',
        'listing_type' => 'Type'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        if ($field === 'listing_website') {
            echo '<input type="url" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="https://example.com" /></p>';
        } else {
            echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        }
    }
}

/**
 * Contact Info Meta Box Callback
 */
function listing_contact_info_callback($post) {
    $fields = array(
        'listing_phone' => 'Phone',
        'listing_email_1' => 'Email',
        'listing_email_1_full_name' => 'Contact Full Name',
        'listing_email_1_first_name' => 'Contact First Name',
        'listing_email_1_last_name' => 'Contact Last Name',
        'listing_email_1_title' => 'Contact Title',
        'listing_email_1_phone' => 'Contact Phone'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
    }
}

/**
 * Location Info Meta Box Callback
 */
function listing_location_info_callback($post) {
    $fields = array(
        'listing_full_address' => 'Full Address',
        'listing_borough' => 'Borough',
        'listing_street' => 'Street',
        'listing_city' => 'City',
        'listing_zip' => 'ZIP Code',
        'listing_state' => 'State',
        'listing_country' => 'Country',
        'listing_lat' => 'Latitude',
        'listing_long' => 'Longitude',
        'listing_h3' => 'H3',
        'listing_area_service' => 'Area Service'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        if (in_array($field, ['listing_lat', 'listing_long'])) {
            echo '<input type="number" step="any" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="' . ($field === 'listing_lat' ? 'e.g., 46.4774' : 'e.g., -30.7226') . '" /></p>';
        } else {
            echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        }
    }
}

/**
 * Business Info Meta Box Callback
 */
function listing_business_info_callback($post) {
    $fields = array(
        'listing_rating' => 'Rating',
        'listing_reviews' => 'Reviews Count',
        'listing_reviews_link' => 'Reviews Link',
        'listing_photo' => 'Photo URL',
        'listing_street_view' => 'Street View URL',
        'listing_about' => 'About',
        'listing_logo' => 'Logo URL',
        'listing_verified' => 'Verified',
        'listing_booking_appointment_link' => 'Booking Link',
        'listing_location_link' => 'Location Link',
        'listing_location_reviews_link' => 'Location Reviews Link',
        'listing_place_id' => 'Place ID',
        'listing_google_id' => 'Google ID',
        'listing_cid' => 'CID',
        'listing_kgmid' => 'KGMID',
        'listing_reviews_id' => 'Reviews ID'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        if ($field === 'listing_about') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<textarea id="' . $field . '" name="' . $field . '" style="width: 100%; height: 100px;">' . esc_textarea($value) . '</textarea></p>';
        } elseif ($field === 'listing_rating') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="number" step="0.1" min="0" max="5" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="0.0 - 5.0" /></p>';
        } elseif ($field === 'listing_reviews') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="number" min="0" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        } elseif (in_array($field, ['listing_photo', 'listing_logo', 'listing_reviews_link', 'listing_booking_appointment_link', 'listing_location_link', 'listing_location_reviews_link', 'listing_street_view'])) {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="url" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        } elseif ($field === 'listing_verified') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<select id="' . $field . '" name="' . $field . '" style="width: 100%;">';
            echo '<option value="">Not Verified</option>';
            echo '<option value="1"' . selected($value, '1', false) . '>Verified</option>';
            echo '</select></p>';
        } else {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        }
    }
}

/**
 * Social Media Meta Box Callback
 */
function listing_social_info_callback($post) {
    $fields = array(
        'listing_facebook' => 'Facebook',
        'listing_instagram' => 'Instagram',
        'listing_linkedin' => 'LinkedIn',
        'listing_tiktok' => 'TikTok',
        'listing_twitter' => 'Twitter',
        'listing_youtube' => 'YouTube'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        echo '<input type="url" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
    }
}

/**
 * Working Hours Meta Box Callback
 */
function listing_hours_info_callback($post) {
    $working_hours = get_post_meta($post->ID, 'listing_working_hours', true);
    $popular_times = get_post_meta($post->ID, 'listing_popular_times', true);

    echo '<p><label for="listing_working_hours">Working Hours (JSON format):</label><br>';
    echo '<textarea id="listing_working_hours" name="listing_working_hours" style="width: 100%; height: 200px;">' . esc_textarea($working_hours) . '</textarea></p>';
    echo '<p><small>Format: {"work_hours": {"timetable": {"monday": [{"open": {"hour": 9, "minute": 0}, "close": {"hour": 17, "minute": 0}}], ...}, "current_status": "open"}}</small></p>';

    echo '<p><label for="listing_popular_times">Popular Times (JSON format):</label><br>';
    echo '<textarea id="listing_popular_times" name="listing_popular_times" style="width: 100%; height: 100px;">' . esc_textarea($popular_times) . '</textarea></p>';
}

/**
 * Save Custom Fields
 */
function save_listing_meta_box_data($post_id) {
    if (!isset($_POST['listing_meta_box_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['listing_meta_box_nonce'], 'listing_meta_box')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (isset($_POST['post_type']) && 'listing' == $_POST['post_type']) {
        if (!current_user_can('edit_page', $post_id)) {
            return;
        }
    } else {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }

    // List of all custom fields (updated to match corrected field names)
    $fields = array(
        'listing_website', 'listing_subtypes', 'listing_category', 'listing_type', 'listing_phone',
        'listing_email_1', 'listing_email_1_full_name', 'listing_email_1_first_name',
        'listing_email_1_last_name', 'listing_email_1_title', 'listing_email_1_phone',
        'listing_full_address', 'listing_borough', 'listing_street', 'listing_city',
        'listing_zip', 'listing_state', 'listing_country',
        'listing_lat', 'listing_long', 'listing_h3', 'listing_area_service', 'listing_rating',
        'listing_reviews', 'listing_reviews_link', 'listing_photo',
        'listing_street_view', 'listing_about', 'listing_logo',
        'listing_verified', 'listing_booking_appointment_link', 'listing_location_link',
        'listing_location_reviews_link', 'listing_place_id', 'listing_google_id',
        'listing_cid', 'listing_kgmid', 'listing_reviews_id', 'listing_facebook',
        'listing_instagram', 'listing_linkedin', 'listing_tiktok', 'listing_twitter',
        'listing_youtube', 'listing_working_hours', 'listing_popular_times'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'save_listing_meta_box_data');

/**
 * Helper function to format working hours
 */
function format_working_hours($working_hours_json) {
    if (empty($working_hours_json)) {
        return array();
    }

    $hours_data = json_decode($working_hours_json, true);
    if (!$hours_data || !isset($hours_data['work_hours']['timetable'])) {
        return array();
    }

    $timetable = $hours_data['work_hours']['timetable'];
    $formatted_hours = array();

    $days = array('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday');

    foreach ($days as $day) {
        if (isset($timetable[$day]) && !empty($timetable[$day])) {
            $day_hours = $timetable[$day][0];
            if ($day_hours['open']['hour'] == 0 && $day_hours['open']['minute'] == 0 &&
                $day_hours['close']['hour'] == 0 && $day_hours['close']['minute'] == 0) {
                $formatted_hours[$day] = 'Closed';
            } else {
                $open_time = sprintf('%02d:%02d', $day_hours['open']['hour'], $day_hours['open']['minute']);
                $close_time = sprintf('%02d:%02d', $day_hours['close']['hour'], $day_hours['close']['minute']);
                $formatted_hours[$day] = $open_time . ' - ' . $close_time;
            }
        } else {
            $formatted_hours[$day] = 'Closed';
        }
    }

    return $formatted_hours;
}

/**
 * Helper function to get listing rating stars
 */
function get_rating_stars($rating) {
    if (empty($rating)) {
        return '';
    }

    $rating = floatval($rating);
    $full_stars = floor($rating);
    $half_star = ($rating - $full_stars) >= 0.5 ? 1 : 0;
    $empty_stars = 5 - $full_stars - $half_star;

    $stars = str_repeat('★', $full_stars);
    if ($half_star) {
        $stars .= '☆';
    }
    $stars .= str_repeat('☆', $empty_stars);

    return $stars;
}

/**
 * Helper function to get social media links
 */
function get_social_media_links($post_id) {
    $social_fields = array(
        'listing_facebook' => 'Facebook',
        'listing_instagram' => 'Instagram',
        'listing_linkedin' => 'LinkedIn',
        'listing_tiktok' => 'TikTok',
        'listing_twitter' => 'Twitter',
        'listing_youtube' => 'YouTube'
    );

    $social_links = array();

    foreach ($social_fields as $field => $label) {
        $url = get_post_meta($post_id, $field, true);
        if (!empty($url)) {
            $social_links[$label] = $url;
        }
    }

    return $social_links;
}

/**
 * Custom search function for listings
 */
function custom_listing_search($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_search()) {
            $query->set('post_type', array('listing'));
        }
    }
}
add_action('pre_get_posts', 'custom_listing_search');

/**
 * Add custom query vars for filtering
 */
function add_custom_query_vars($vars) {
    $vars[] = 'listing_city';
    $vars[] = 'listing_rating';
    $vars[] = 'directory_filter';
    return $vars;
}
add_filter('query_vars', 'add_custom_query_vars');

/**
 * Modify main query for filtering
 */
function modify_main_query($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_home() || is_front_page() || is_post_type_archive('listing')) {
            $query->set('post_type', 'listing');
            $query->set('posts_per_page', 12);

            // Handle city filter
            $city_filter = get_query_var('listing_city');
            if (!empty($city_filter)) {
                $meta_query = $query->get('meta_query') ?: array();
                $meta_query[] = array(
                    'key' => 'listing_city',
                    'value' => $city_filter,
                    'compare' => 'LIKE'
                );
                $query->set('meta_query', $meta_query);
            }

            // Handle rating filter
            $rating_filter = get_query_var('listing_rating');
            if (!empty($rating_filter)) {
                $meta_query = $query->get('meta_query') ?: array();
                $meta_query[] = array(
                    'key' => 'listing_rating',
                    'value' => $rating_filter,
                    'compare' => '>='
                );
                $query->set('meta_query', $meta_query);
            }

            // Handle directory taxonomy filter
            $directory_filter = get_query_var('directory_filter');
            if (!empty($directory_filter)) {
                $query->set('directory', $directory_filter);
            }
        }
    }
}
add_action('pre_get_posts', 'modify_main_query');

/**
 * Convert listing_subtypes custom field to directory taxonomy terms
 * This function migrates data from the listing_subtypes custom field to the directory taxonomy
 */
function convert_listing_subtypes_to_taxonomy($remove_old_field = false, $dry_run = false) {
    // Initialize counters and logs
    $migration_log = array(
        'total_listings_processed' => 0,
        'listings_with_subtypes' => 0,
        'new_terms_created' => 0,
        'term_assignments' => 0,
        'errors' => array(),
        'created_terms' => array(),
        'processed_listings' => array()
    );

    // Get all listings that have the listing_subtypes field
    $listings_query = new WP_Query(array(
        'post_type' => 'listing',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'listing_subtypes',
                'value' => '',
                'compare' => '!='
            )
        ),
        'fields' => 'ids'
    ));

    $migration_log['total_listings_processed'] = $listings_query->found_posts;

    if (!$listings_query->have_posts()) {
        $migration_log['message'] = 'No listings found with listing_subtypes field.';
        return $migration_log;
    }

    foreach ($listings_query->posts as $listing_id) {
        $subtypes_value = get_post_meta($listing_id, 'listing_subtypes', true);

        if (empty($subtypes_value)) {
            continue;
        }

        $migration_log['listings_with_subtypes']++;
        $listing_title = get_the_title($listing_id);

        // Parse subtypes - handle multiple delimiters (comma, semicolon, pipe, newline)
        $subtypes_raw = preg_split('/[,;|\n\r]+/', $subtypes_value);
        $subtypes = array();

        foreach ($subtypes_raw as $subtype) {
            $subtype = trim($subtype);
            if (!empty($subtype)) {
                $subtypes[] = $subtype;
            }
        }

        if (empty($subtypes)) {
            continue;
        }

        $assigned_terms = array();

        foreach ($subtypes as $subtype) {
            // Check if term already exists
            $existing_term = get_term_by('name', $subtype, 'directory');

            if ($existing_term) {
                $term_id = $existing_term->term_id;
            } else {
                // Create new term if it doesn't exist
                if (!$dry_run) {
                    $new_term = wp_insert_term($subtype, 'directory');

                    if (is_wp_error($new_term)) {
                        $migration_log['errors'][] = "Failed to create term '{$subtype}' for listing '{$listing_title}' (ID: {$listing_id}): " . $new_term->get_error_message();
                        continue;
                    }

                    $term_id = $new_term['term_id'];
                    $migration_log['new_terms_created']++;
                    $migration_log['created_terms'][] = $subtype;
                } else {
                    // In dry run mode, simulate term creation
                    $term_id = 'DRY_RUN_' . sanitize_title($subtype);
                    $migration_log['new_terms_created']++;
                    $migration_log['created_terms'][] = $subtype . ' (DRY RUN)';
                }
            }

            $assigned_terms[] = $term_id;
        }

        // Assign terms to the listing
        if (!empty($assigned_terms) && !$dry_run) {
            $result = wp_set_object_terms($listing_id, $assigned_terms, 'directory', true);

            if (is_wp_error($result)) {
                $migration_log['errors'][] = "Failed to assign terms to listing '{$listing_title}' (ID: {$listing_id}): " . $result->get_error_message();
            } else {
                $migration_log['term_assignments']++;
                $migration_log['processed_listings'][] = array(
                    'id' => $listing_id,
                    'title' => $listing_title,
                    'original_subtypes' => $subtypes_value,
                    'parsed_subtypes' => $subtypes,
                    'assigned_terms' => $assigned_terms
                );
            }
        } elseif (!empty($assigned_terms) && $dry_run) {
            // In dry run mode, simulate term assignment
            $migration_log['term_assignments']++;
            $migration_log['processed_listings'][] = array(
                'id' => $listing_id,
                'title' => $listing_title,
                'original_subtypes' => $subtypes_value,
                'parsed_subtypes' => $subtypes,
                'assigned_terms' => $assigned_terms . ' (DRY RUN)'
            );
        }

        // Remove old custom field if requested and not in dry run mode
        if ($remove_old_field && !$dry_run && !empty($assigned_terms)) {
            delete_post_meta($listing_id, 'listing_subtypes');
        }
    }

    // Add summary message
    if ($dry_run) {
        $migration_log['message'] = 'DRY RUN completed. No actual changes were made.';
    } else {
        $migration_log['message'] = 'Migration completed successfully.';
    }

    return $migration_log;
}

/**
 * Helper function to display migration results in a readable format
 */
function display_migration_results($migration_log) {
    echo "<div style='background: #f1f1f1; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>Migration Results</h3>";
    echo "<p><strong>Status:</strong> " . esc_html($migration_log['message']) . "</p>";
    echo "<p><strong>Total listings found:</strong> " . intval($migration_log['total_listings_processed']) . "</p>";
    echo "<p><strong>Listings with subtypes:</strong> " . intval($migration_log['listings_with_subtypes']) . "</p>";
    echo "<p><strong>New terms created:</strong> " . intval($migration_log['new_terms_created']) . "</p>";
    echo "<p><strong>Term assignments made:</strong> " . intval($migration_log['term_assignments']) . "</p>";

    if (!empty($migration_log['created_terms'])) {
        echo "<h4>New Terms Created:</h4>";
        echo "<ul>";
        foreach ($migration_log['created_terms'] as $term) {
            echo "<li>" . esc_html($term) . "</li>";
        }
        echo "</ul>";
    }

    if (!empty($migration_log['errors'])) {
        echo "<h4 style='color: red;'>Errors:</h4>";
        echo "<ul style='color: red;'>";
        foreach ($migration_log['errors'] as $error) {
            echo "<li>" . esc_html($error) . "</li>";
        }
        echo "</ul>";
    }

    if (!empty($migration_log['processed_listings'])) {
        echo "<h4>Processed Listings (first 10):</h4>";
        echo "<ul>";
        $count = 0;
        foreach ($migration_log['processed_listings'] as $listing) {
            if ($count >= 10) break;
            echo "<li><strong>" . esc_html($listing['title']) . "</strong> (ID: " . intval($listing['id']) . ")<br>";
            echo "Original: " . esc_html($listing['original_subtypes']) . "<br>";
            echo "Parsed: " . esc_html(implode(', ', $listing['parsed_subtypes'])) . "</li>";
            $count++;
        }
        if (count($migration_log['processed_listings']) > 10) {
            echo "<li><em>... and " . (count($migration_log['processed_listings']) - 10) . " more listings</em></li>";
        }
        echo "</ul>";
    }

    echo "</div>";
}

/**
 * Add admin menu for migration tool
 */
function add_listing_migration_admin_menu() {
    add_submenu_page(
        'edit.php?post_type=listing',
        'Migrate Subtypes to Taxonomy',
        'Migrate Subtypes',
        'manage_options',
        'listing-migration',
        'listing_migration_admin_page'
    );
}
add_action('admin_menu', 'add_listing_migration_admin_menu');

/**
 * Admin page for the migration tool
 */
function listing_migration_admin_page() {
    // Handle form submission
    if (isset($_POST['run_migration'])) {
        if (!wp_verify_nonce($_POST['migration_nonce'], 'listing_migration')) {
            wp_die('Security check failed');
        }

        $dry_run = isset($_POST['dry_run']) ? true : false;
        $remove_old_field = isset($_POST['remove_old_field']) ? true : false;

        echo "<div class='wrap'>";
        echo "<h1>Listing Subtypes Migration</h1>";

        $migration_log = convert_listing_subtypes_to_taxonomy($remove_old_field, $dry_run);
        display_migration_results($migration_log);

        echo "</div>";
        return;
    }

    // Display the form
    ?>
    <div class="wrap">
        <h1>Migrate Listing Subtypes to Directory Taxonomy</h1>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ccd0d4; border-radius: 4px;">
            <h2>About This Migration</h2>
            <p>This tool will convert the <code>listing_subtypes</code> custom field values into terms in the <code>directory</code> taxonomy.</p>

            <h3>What this migration does:</h3>
            <ul>
                <li>Finds all listings with the <code>listing_subtypes</code> custom field</li>
                <li>Parses multiple subtypes (separated by commas, semicolons, pipes, or newlines)</li>
                <li>Creates new taxonomy terms in the <code>directory</code> taxonomy for each unique subtype</li>
                <li>Assigns the appropriate terms to each listing</li>
                <li>Optionally removes the old custom field data after successful migration</li>
            </ul>

            <h3>Safety Features:</h3>
            <ul>
                <li><strong>Dry Run Mode:</strong> Test the migration without making any changes</li>
                <li><strong>Detailed Logging:</strong> See exactly what will be changed</li>
                <li><strong>Error Handling:</strong> Continues processing even if individual items fail</li>
                <li><strong>Idempotent:</strong> Safe to run multiple times</li>
            </ul>
        </div>

        <form method="post" action="">
            <?php wp_nonce_field('listing_migration', 'migration_nonce'); ?>

            <table class="form-table">
                <tr>
                    <th scope="row">Migration Mode</th>
                    <td>
                        <label>
                            <input type="checkbox" name="dry_run" value="1" checked>
                            <strong>Dry Run</strong> - Preview changes without making them (recommended first)
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Remove Old Field</th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_old_field" value="1">
                            Remove <code>listing_subtypes</code> custom field after successful migration
                        </label>
                        <p class="description">Only recommended after confirming the migration worked correctly</p>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="run_migration" class="button-primary" value="Run Migration">
            </p>
        </form>
    </div>
    <?php
}

/**
 * Quick migration function for programmatic use
 * Usage examples:
 * - run_listing_subtypes_migration(); // Dry run only
 * - run_listing_subtypes_migration(false); // Actual migration, keep old field
 * - run_listing_subtypes_migration(false, true); // Actual migration, remove old field
 */
function run_listing_subtypes_migration($dry_run = true, $remove_old_field = false) {
    $migration_log = convert_listing_subtypes_to_taxonomy($remove_old_field, $dry_run);

    // Output results for CLI or debugging
    if (defined('WP_CLI') && WP_CLI) {
        WP_CLI::line('=== Listing Subtypes Migration Results ===');
        WP_CLI::line('Status: ' . $migration_log['message']);
        WP_CLI::line('Total listings found: ' . $migration_log['total_listings_processed']);
        WP_CLI::line('Listings with subtypes: ' . $migration_log['listings_with_subtypes']);
        WP_CLI::line('New terms created: ' . $migration_log['new_terms_created']);
        WP_CLI::line('Term assignments made: ' . $migration_log['term_assignments']);

        if (!empty($migration_log['created_terms'])) {
            WP_CLI::line('New terms: ' . implode(', ', $migration_log['created_terms']));
        }

        if (!empty($migration_log['errors'])) {
            WP_CLI::warning('Errors occurred:');
            foreach ($migration_log['errors'] as $error) {
                WP_CLI::line('  - ' . $error);
            }
        }
    } else {
        // For web/debug output
        error_log('Listing Subtypes Migration: ' . print_r($migration_log, true));
    }

    return $migration_log;
}
